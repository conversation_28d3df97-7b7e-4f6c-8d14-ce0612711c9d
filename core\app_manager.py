#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用管理模块
负责应用的启动和运行时配置
"""

import subprocess
from pathlib import Path
from typing import Optional

class AppManager:
    """应用管理类"""
    def __init__(self, env_manager, state_manager):
        self.env_manager = env_manager
        self.state_manager = state_manager
        self.port = 8501

    def start_app(self) -> bool:
        """启动应用
        
        Returns:
            bool: 启动是否成功
        """
        print("\nLaunching EasyReporter...")
        print(f"Web app will be available at: http://localhost:{self.port}")
        
        try:
            process = subprocess.Popen(
                [
                    self.env_manager.get_venv_python(),
                    "-m", "streamlit", "run", "streamlit_app.py",
                    "--server.port", str(self.port)
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 读取并显示输出，直到看到启动成功的标志
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                print(line.strip())
                # 检查是否包含成功启动的标志
                if "You can now view your Streamlit app in your browser" in line:
                    return True
                # 检查是否有错误
                if "Error" in line or "Exception" in line:
                    return False
                    
            return process.poll() is None  # 如果进程还在运行，认为启动成功
            
        except Exception as e:
            print(f"Failed to start application: {e}")
            return False

    def check_port_available(self, port: Optional[int] = None) -> bool:
        """检查端口是否可用
        
        Args:
            port: 要检查的端口号，如果为None则检查默认端口
            
        Returns:
            bool: 端口是否可用
        """
        import socket
        port = port or self.port
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            sock.bind(('localhost', port))
            return True
        except:
            return False
        finally:
            sock.close()

    def find_available_port(self, start_port: int = 8501) -> int:
        """查找可用端口
        
        Args:
            start_port: 起始端口号
            
        Returns:
            int: 可用的端口号
        """
        port = start_port
        while not self.check_port_available(port) and port < start_port + 100:
            port += 1
        return port

    def setup_runtime(self) -> bool:
        """配置运行时环境
        
        Returns:
            bool: 配置是否成功
        """
        # 检查端口
        if not self.check_port_available():
            new_port = self.find_available_port()
            if new_port != self.port:
                print(f"Port {self.port} is in use, switching to port {new_port}")
                self.port = new_port
        
        # 可以在这里添加其他运行时配置
        return True
