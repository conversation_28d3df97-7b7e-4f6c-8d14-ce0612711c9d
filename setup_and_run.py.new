#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EasyReporter Setup and Run Script
负责整个应用的初始化和启动流程
"""
import sys
from datetime import datetime
from pathlib import Path

# Import core managers
from core.state_manager import StateManager
from core.environment_manager import EnvironmentManager
from core.package_manager import PackageManager
from core.gpu_manager import GPUManager
from core.cellpose_manager import CellposeManager
from core.app_manager import AppManager

class ApplicationRunner:
    """应用启动管理类"""
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        
        # 初始化各个管理器
        self.state_manager = StateManager(self.project_root)
        self.env_manager = EnvironmentManager(self.project_root, self.state_manager)
        self.package_manager = PackageManager(self.env_manager.venv_path, self.env_manager.is_windows)
        self.gpu_manager = GPUManager(self.package_manager, self.state_manager, self.env_manager)
        self.cellpose_manager = CellposeManager(self.package_manager, self.state_manager, self.env_manager)
        self.app_manager = AppManager(self.env_manager, self.state_manager)

    def run(self):
        """运行应用程序的主要流程"""
        print("=" * 50)
        print(" EasyReporter Environment Setup and Launch")
        print("=" * 50)

        try:
            # 检查和设置环境
            if not self.env_manager.check_environment():
                if not self.env_manager.setup_environment():
                    return False
            
            # 安装依赖
            if not self.state_manager.get_state('installation', 'completed'):
                if not self.package_manager.install_requirements():
                    return False
                self.state_manager.update_state({
                    'installation': {
                        'completed': True,
                        'timestamp': datetime.now().isoformat()
                    }
                })
            
            # 检查 GPU 支持
            gpu_status = self.gpu_manager.check_gpu_support()
            if not gpu_status:
                print("Warning: GPU support not available, using CPU mode")
            
            # 测试 Cellpose
            if not self.state_manager.get_state('cellpose', 'tested'):
                if not self.cellpose_manager.test_cellpose():
                    print("Warning: Cellpose test failed, but continuing...")
            
            # 启动应用
            return self.app_manager.start_application()
            
        except KeyboardInterrupt:
            print("\nOperation interrupted by user")
            return False
        except Exception as e:
            print(f"\nError occurred: {str(e)}")
            return False

def main():
    runner = ApplicationRunner()
    success = runner.run()
    if not success:
        print("\nLaunch failed, please check the error messages above")
        input("Press Enter to exit...")
        return 1
    return 0

if __name__ == "__main__":
    sys.exit(main())
