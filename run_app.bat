@echo off
chcp 65001 >nul
REM EasyReporter Startup Script
setlocal EnableDelayedExpansion

REM Set environment variables
set "PYTHONIOENCODING=utf-8"
set "PYTHONUTF8=1"

title EasyReporter

echo ========================================
echo    EasyReporter Startup
echo ========================================
echo.

REM Check Environment
echo [1/3] Checking environment...
call python check_environment.py
if !errorlevel! neq 0 (
    echo Environment check failed. Please follow the suggestions above.
    pause
    exit /b 1
)
echo Environment check passed.

REM Install dependencies if needed
if not exist ".installed" (
    echo.
    echo [2/3] Installing dependencies...
    echo This may take 5-10 minutes, please wait...
    echo.

    echo Upgrading pip...
    call python -m pip install --upgrade pip

    echo Installing dependencies...
    call python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Installation failed, trying with China mirror...
        python -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
        if errorlevel 1 (
            echo ERROR: Package installation failed
            pause
            exit /b 1
        )
    )

    echo.
    echo Checking GPU support...
    python install_gpu_support.py
    if errorlevel 1 (
        echo WARNING: GPU support not available, continuing with CPU mode...
    ) else (
        echo GPU support configured successfully
    )

    echo Installing from requirements.txt...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Installation failed, trying with China mirror...
        python -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
        if errorlevel 1 (
            echo ERROR: Package installation failed
            pause
            exit /b 1
        )
    )

    echo Running environment test...
    python quick_cellpose_test.py
    if errorlevel 1 (
        echo WARNING: Environment test failed, but continuing...
    )

    echo. > .installed
    echo Dependencies installed successfully
) else (
    echo [2/3] Dependencies already installed
)

REM Verify Cellpose
echo.
echo Checking Cellpose...
python -c "import cellpose; print('Cellpose version:', cellpose.__version__ if hasattr(cellpose, '__version__') else '3.0.8')" 2>nul
if errorlevel 1 (
    echo Cellpose verification failed, reinstalling...
    python -m pip install cellpose==3.0.8
    if errorlevel 1 (
        echo ERROR: Cellpose installation failed
        pause
        exit /b 1
    )
)
echo Cellpose OK

REM Start application
echo.
echo [3/3] Starting EasyReporter...
echo.
echo Web app will open in your browser
echo If not, visit: http://localhost:8501
echo.
echo Keep this window open - closing it will stop the app
echo Press Ctrl+C to stop
echo.
echo ========================================

streamlit run streamlit_app.py --server.port 8501

echo.
echo App stopped
pause
