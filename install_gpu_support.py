#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU支持安装脚本
"""
import subprocess
import sys
import os
import platform
from pathlib import Path

def get_pip_executable():
    """获取最佳的pip可执行文件路径"""
    # 首先尝试使用虚拟环境中的pip（如果存在）
    project_root = Path(__file__).parent.absolute()
    venv_path = project_root / ".venv"
    is_windows = platform.system().lower() == "windows"

    if venv_path.exists():
        if is_windows:
            venv_pip = venv_path / "Scripts" / "pip.exe"
        else:
            venv_pip = venv_path / "bin" / "pip"

        if venv_pip.exists():
            return str(venv_pip)

    # 如果虚拟环境不存在，使用系统pip
    return [sys.executable, "-m", "pip"]

def run_pip(command):
    """运行pip命令"""
    try:
        # 设置环境变量启用进度条
        my_env = os.environ.copy()
        my_env["PIP_PROGRESS_BAR"] = "on"   # 启用进度条
        my_env["PYTHONUNBUFFERED"] = "1"    # 禁用输出缓冲
        my_env["FORCE_COLOR"] = "1"         # 强制彩色输出
        my_env["TERM"] = "xterm-256color"   # 设置终端类型支持颜色

        # 获取pip可执行文件
        pip_executable = get_pip_executable()

        # 构建命令
        if isinstance(pip_executable, list):
            # 使用 python -m pip 格式
            args = pip_executable.copy()
        else:
            # 使用直接的pip可执行文件
            args = [pip_executable]

        # 解析命令并添加参数
        cmd_parts = command.split()
        if cmd_parts and cmd_parts[0] == "install":
            # 对于install命令，添加进度条和其他优化参数
            args.extend(cmd_parts[:1])  # 添加 "install"
            args.extend([
                "--progress-bar=on",             # 启用进度条（使用=号格式）
                "--disable-pip-version-check",   # 禁用版本检查
                "--no-cache-dir"                 # 禁用缓存以确保实时下载
            ])
            args.extend(cmd_parts[1:])  # 添加剩余参数
        else:
            # 对于其他命令，直接添加
            args.extend(cmd_parts)

        # 使用 subprocess.run 并直接输出到终端以获得最佳进度条显示
        result = subprocess.run(
            args,
            env=my_env,
            text=True,
            bufsize=0,  # 完全禁用缓冲
            stdout=None,  # 直接输出到终端
            stderr=None,  # 直接输出到终端
            timeout=600   # 10分钟超时
        )

        return result.returncode == 0

    except subprocess.TimeoutExpired:
        print("\n安装超时，可能是网络问题")
        return False
    except Exception as e:
        print(f"安装出错: {e}")
        return False

def check_python_version():
    """检查Python版本兼容性"""
    version = sys.version_info
    if version.major == 3 and version.minor > 11:
        print(f"\n⚠️ 警告: 当前Python版本 ({sys.version.split()[0]}) 较高")
        print("PyTorch官方推荐使用Python 3.10或3.11")
        print("但我们将尝试安装最新版本的PyTorch")
        print("\n如果安装失败，建议:")
        print("1. 安装Python 3.10或3.11版本")
        print("2. 创建新的虚拟环境：")
        print("   conda create -n easyreporter python=3.10")
        print("   conda activate easyreporter")
        # 不再直接返回False，而是继续尝试安装
    return True

def check_cuda():
    """检查CUDA环境"""
    try:
        import ctypes
        # 尝试加载CUDA库来检查是否存在
        ctypes.CDLL("nvcuda.dll")

        # 获取NVIDIA驱动版本
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=driver_version', '--format=csv,noheader'],
                                 capture_output=True, text=True)
            if result.returncode == 0:
                driver_version = result.stdout.strip()
                print(f"NVIDIA驱动版本: {driver_version}")
        except:
            pass

        return True
    except:
        return False

def install_gpu_support():
    """安装GPU支持"""
    print("正在检查环境...")
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查CUDA环境
    if not check_cuda():
        print("[!] 未检测到CUDA环境")
        print("请确保：")
        print("1. 安装了NVIDIA显卡驱动")
        print("2. 安装了CUDA工具包 (建议版本 12.1)")
        print("可以从 https://developer.nvidia.com/cuda-downloads 下载CUDA")
        return False
    
    print("[+] 检测到CUDA环境")
    
    try:
        import torch
        if hasattr(torch, 'cuda') and torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            for i in range(device_count):
                print(f"已检测到GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"当前PyTorch版本: {torch.__version__}")
            print(f"CUDA版本: {torch.version.cuda}")
            print("GPU支持已配置完成")
            return True
    except (ImportError, AttributeError):
        pass
    
    print("正在安装GPU支持...")
    
    try:
        # 先尝试强制卸载torch相关包
        import shutil
        site_packages = os.path.dirname(os.__file__) + "\\site-packages"
        torch_paths = [
            os.path.join(site_packages, name)
            for name in os.listdir(site_packages)
            if name.startswith("torch") or name == "-orch"
        ]
        for path in torch_paths:
            try:
                if os.path.isdir(path):
                    shutil.rmtree(path, ignore_errors=True)
                else:
                    os.remove(path)
            except:
                pass
    except:
        pass
    
    # 尝试安装CUDA版本
    print("\n开始安装PyTorch...")
    commands = [
        "install torch==2.1.2+cu121 torchvision==0.16.2+cu121 --index-url https://download.pytorch.org/whl/cu121",
        "install torch==2.1.2+cu121 torchvision==0.16.2+cu121 torchaudio==2.1.2+cu121 --index-url https://mirrors.aliyun.com/pytorch/whl/cu121",
        "install torch==2.1.2+cu121 torchvision==0.16.2+cu121 --extra-index-url https://mirrors.tuna.tsinghua.edu.cn/pytorch/whl/cu121",
        # 尝试安装最新版本
        "install torch torchvision --index-url https://download.pytorch.org/whl/cu118",
        "install torch torchvision --extra-index-url https://mirrors.tuna.tsinghua.edu.cn/pytorch/whl/cu118"
    ]
    
    for cmd in commands:
        print(f"\n尝试安装命令: pip {cmd}")
        if run_pip(cmd):
            break
    
    # 验证安装
    try:
        import torch
        if hasattr(torch, 'cuda') and torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            print(f"\n[+] GPU支持安装成功！")
            for i in range(device_count):
                print(f"  检测到GPU {i}: {torch.cuda.get_device_name(i)}")
            return True
        else:
            print("\n[!] GPU支持安装失败")
            print("可能的原因：")
            print("1. 系统中没有兼容的NVIDIA显卡")
            print("2. NVIDIA驱动程序未安装或版本过低")
            print("3. CUDA工具包未安装或版本不兼容")
            print("\n建议：")
            print("1. 确保安装了最新的NVIDIA显卡驱动")
            print("2. 安装CUDA 12.1或更高版本")
            print("3. 如果问题仍然存在，可以继续使用CPU模式")
            return False
    except (ImportError, AttributeError):
        print("\n❌ PyTorch安装失败")
        return False

if __name__ == "__main__":
    success = install_gpu_support()
    sys.exit(0 if success else 1)
