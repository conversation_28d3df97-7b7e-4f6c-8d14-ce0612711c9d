#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的包管理模块
处理所有pip相关的操作
"""

import os
import subprocess
from pathlib import Path
from typing import List, Optional, Union, Dict

class PackageManager:
    """统一的包管理类"""
    def __init__(self, venv_path: Path, is_windows: bool = True):
        self.venv_path = venv_path
        self.is_windows = is_windows
        self.pip_command = "pip.exe" if is_windows else "pip"
        self._setup_env()

    def _setup_env(self) -> None:
        """设置环境变量"""
        self.env = os.environ.copy()
        self.env.update({
            "PIP_PROGRESS_BAR": "on",         # 启用进度条
            "PYTHONUNBUFFERED": "1",          # 禁用输出缓冲
            "FORCE_COLOR": "1",               # 强制彩色输出
            "TERM": "xterm-256color"          # 设置终端类型支持颜色
        })

    def get_pip_path(self) -> str:
        """获取pip可执行文件路径"""
        if self.is_windows:
            pip_path = self.venv_path / "Scripts" / self.pip_command
        else:
            pip_path = self.venv_path / "bin" / self.pip_command
        return str(pip_path)

    def run_pip_install(self, 
                       packages: Union[str, List[str]], 
                       options: Optional[Dict[str, str]] = None,
                       use_mirror: bool = False) -> bool:
        """执行pip安装
        
        Args:
            packages: 要安装的包名或包列表
            options: 额外的pip选项
            use_mirror: 是否使用镜像源
            
        Returns:
            bool: 安装是否成功
        """
        if isinstance(packages, str):
            packages = [packages]

        # 基本命令参数
        args = [
            self.get_pip_path(),
            "install",
            "--progress-bar=on",
            "--disable-pip-version-check",
            "--no-cache-dir"
        ]

        # 添加镜像源
        if use_mirror:
            args.extend(["-i", "https://pypi.tuna.tsinghua.edu.cn/simple"])

        # 添加额外选项
        if options:
            for key, value in options.items():
                if value:
                    args.extend([f"--{key}", value])
                else:
                    args.append(f"--{key}")

        # 添加包
        args.extend(packages)

        try:
            result = subprocess.run(
                args,
                env=self.env,
                text=True,
                check=False,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )

            # 输出安装日志
            if result.stdout:
                print(result.stdout)

            return result.returncode == 0
        except Exception as e:
            print(f"Installation error: {e}")
            return False

    def install_requirements(self, requirements_file: Path, attempts: int = 2) -> bool:
        """安装requirements.txt中的依赖
        
        Args:
            requirements_file: requirements.txt文件路径
            attempts: 尝试次数（第二次会使用镜像源）
            
        Returns:
            bool: 安装是否成功
        """
        print("\nInstalling project dependencies...")
        success = self.run_pip_install(["-r", str(requirements_file)])

        if not success and attempts > 1:
            print("\nRetrying with mirror source...")
            success = self.run_pip_install(
                ["-r", str(requirements_file)],
                use_mirror=True
            )

        return success

    def upgrade_pip(self, quiet: bool = True) -> bool:
        """升级pip
        
        Args:
            quiet: 是否静默升级
            
        Returns:
            bool: 升级是否成功
        """
        options = {"quiet": None} if quiet else {}
        return self.run_pip_install("--upgrade pip", options=options)

    def uninstall_package(self, package: str, force: bool = True) -> bool:
        """卸载包
        
        Args:
            package: 包名
            force: 是否强制卸载
            
        Returns:
            bool: 卸载是否成功
        """
        args = [
            self.get_pip_path(),
            "uninstall",
            "-y" if force else "",
            package
        ]

        try:
            result = subprocess.run(
                args,
                env=self.env,
                text=True,
                check=False,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT
            )
            
            if result.stdout:
                print(result.stdout)
                
            return result.returncode == 0
        except Exception as e:
            print(f"Uninstall error: {e}")
            return False
