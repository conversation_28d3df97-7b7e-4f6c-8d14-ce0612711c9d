#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU支持管理模块
负责GPU环境的检测和配置
"""

import subprocess
import ctypes
import shutil
import os
import platform
import sys
from pathlib import Path
from typing import Tuple, Optional

class GPUManager:
    """GPU支持管理类"""
    def __init__(self, package_manager, state_manager, env_manager):
        self.package_manager = package_manager
        self.state_manager = state_manager
        self.env_manager = env_manager
        self.is_windows = platform.system().lower() == "windows"

    def check_cuda(self) -> Tuple[bool, Optional[str]]:
        """检查CUDA环境
        
        Returns:
            Tuple[bool, Optional[str]]: (是否有CUDA环境, 驱动版本)
        """
        try:
            if self.is_windows:
                # Windows下检查CUDA
                ctypes.CDLL("nvcuda.dll")
            else:
                # Linux下检查CUDA
                ctypes.CDLL("libcuda.so")

            # 获取NVIDIA驱动版本
            try:
                result = subprocess.run(
                    ['nvidia-smi', '--query-gpu=driver_version', '--format=csv,noheader'],
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    driver_version = result.stdout.strip()
                    print(f"NVIDIA驱动版本: {driver_version}")
                    return True, driver_version
            except:
                pass

            return True, None
        except:
            return False, None

    def check_python_version(self) -> bool:
        """检查Python版本兼容性"""
        version = sys.version_info
        if version.major == 3 and version.minor > 11:
            print(f"\n⚠️ 警告: 当前Python版本 ({sys.version.split()[0]}) 较高")
            print("PyTorch官方推荐使用Python 3.10或3.11")
            print("但我们将尝试安装最新版本的PyTorch")
            print("\n如果安装失败，建议:")
            print("1. 安装Python 3.10或3.11版本")
            print("2. 创建新的虚拟环境：")
            print("   conda create -n easyreporter python=3.10")
            print("   conda activate easyreporter")
        return True

    def check_torch_gpu(self) -> bool:
        """检查PyTorch的GPU支持
        
        Returns:
            bool: 是否支持GPU
        """
        try:
            import torch
            if hasattr(torch, 'cuda') and torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                for i in range(device_count):
                    print(f"已检测到GPU {i}: {torch.cuda.get_device_name(i)}")
                print(f"当前PyTorch版本: {torch.__version__}")
                print(f"CUDA版本: {torch.version.cuda}")
                return True
        except:
            pass
        return False

    def cleanup_torch_packages(self):
        """清理已安装的torch相关包"""
        try:
            site_packages = os.path.dirname(os.__file__) + "\\site-packages"
            torch_paths = [
                os.path.join(site_packages, name)
                for name in os.listdir(site_packages)
                if name.startswith("torch") or name == "-orch"
            ]
            for path in torch_paths:
                try:
                    if os.path.isdir(path):
                        shutil.rmtree(path, ignore_errors=True)
                    else:
                        os.remove(path)
                except:
                    pass
        except:
            pass

    def setup_gpu_support(self) -> bool:
        """配置GPU支持
        
        Returns:
            bool: 配置是否成功
        """
        print("正在检查环境...")
        
        # 检查是否已配置
        status = self.state_manager.get_state("gpu", "status")
        if status:
            print(f"GPU status: {status}")
            return status == "success"

        # 检查Python版本
        if not self.check_python_version():
            return False
        
        # 检查CUDA环境
        cuda_available, driver_version = self.check_cuda()
        if not cuda_available:
            print("[!] 未检测到CUDA环境")
            print("请确保：")
            print("1. 安装了NVIDIA显卡驱动")
            print("2. 安装了CUDA工具包 (建议版本 12.1)")
            print("可以从 https://developer.nvidia.com/cuda-downloads 下载CUDA")
            self.state_manager.update_component_state("gpu", {"status": "failed"})
            return False
        
        print("[+] 检测到CUDA环境")
        
        # 检查当前PyTorch是否支持GPU
        if self.check_torch_gpu():
            import torch
            self.state_manager.update_component_state("gpu", {
                "status": "success",
                "version": torch.__version__
            })
            return True
        
        print("正在安装GPU支持...")
        
        # 清理已有的torch包
        self.cleanup_torch_packages()
        
        # 更新pip
        print("\n更新pip...")
        self.package_manager.run_pip_install(["--upgrade", "pip"])
        
        # 尝试安装GPU版本的PyTorch
        commands = [
            "torch==2.1.2+cu121 torchvision==0.16.2+cu121 --index-url https://download.pytorch.org/whl/cu121",
            "torch==2.1.2+cu121 torchvision==0.16.2+cu121 torchaudio==2.1.2+cu121 --index-url https://mirrors.aliyun.com/pytorch/whl/cu121",
            "torch==2.1.2+cu121 torchvision==0.16.2+cu121 --extra-index-url https://mirrors.tuna.tsinghua.edu.cn/pytorch/whl/cu121",
            "torch torchvision --index-url https://download.pytorch.org/whl/cu118",
            "torch torchvision --extra-index-url https://mirrors.tuna.tsinghua.edu.cn/pytorch/whl/cu118"
        ]
        
        success = False
        for cmd in commands:
            print(f"\n尝试安装命令: pip {cmd}")
            if self.package_manager.run_pip_install(cmd.split()):
                success = True
                break
        
        # 验证安装
        if success and self.check_torch_gpu():
            import torch
            self.state_manager.update_component_state("gpu", {
                "status": "success",
                "version": torch.__version__
            })
            return True
        else:
            print("\n[!] GPU支持安装失败")
            print("可能的原因：")
            print("1. 系统中没有兼容的NVIDIA显卡")
            print("2. NVIDIA驱动程序未安装或版本过低")
            print("3. CUDA工具包未安装或版本不兼容")
            print("\n建议：")
            print("1. 确保安装了最新的NVIDIA显卡驱动")
            print("2. 安装CUDA 12.1或更高版本")
            print("3. 如果问题仍然存在，可以继续使用CPU模式")
            self.state_manager.update_component_state("gpu", {"status": "failed"})
            return False

    def get_gpu_info(self) -> dict:
        """获取GPU信息
        
        Returns:
            dict: GPU信息字典
        """
        info = {
            "cuda_available": False,
            "driver_version": None,
            "torch_gpu": False,
            "device_count": 0,
            "devices": []
        }
        
        # 检查CUDA
        cuda_available, driver_version = self.check_cuda()
        info["cuda_available"] = cuda_available
        info["driver_version"] = driver_version
        
        # 检查PyTorch
        try:
            import torch
            if hasattr(torch, 'cuda') and torch.cuda.is_available():
                info["torch_gpu"] = True
                info["device_count"] = torch.cuda.device_count()
                info["devices"] = [
                    {
                        "id": i,
                        "name": torch.cuda.get_device_name(i)
                    }
                    for i in range(info["device_count"])
                ]
        except:
            pass
            
        return info
