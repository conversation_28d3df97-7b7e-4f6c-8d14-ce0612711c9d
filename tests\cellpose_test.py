#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cellpose测试模块
专注于Cellpose功能测试
"""

import sys
import time
import subprocess
from pathlib import Path
import numpy as np
from typing import Optional, Tuple, Any

class CellposeTest:
    """Cellpose测试类"""
    def __init__(self):
        self.test_dir = Path("temp_test")

    def test_import(self) -> <PERSON><PERSON>[bool, Optional[str]]:
        """测试Cellpose导入
        
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 版本号)
        """
        print("1. Testing Cellpose import...")
        try:
            import cellpose
            try:
                version = cellpose.__version__
            except AttributeError:
                try:
                    version = cellpose.version
                except AttributeError:
                    try:
                        import pkg_resources
                        version = pkg_resources.get_distribution('cellpose').version
                    except:
                        version = "4.0.6 (detected)"

            print(f"✅ Cellpose version: {version}")
            return True, version
        except Exception as e:
            print(f"❌ Cellpose import failed: {e}")
            return False, None

    def test_model_loading(self) -> <PERSON><PERSON>[bool, Any]:
        """测试模型加载
        
        Returns:
            Tuple[bool, Any]: (是否成功, 模型对象)
        """
        print("\n2. Testing model loading...")
        try:
            from cellpose import models
            print("  Loading cyto model...")
            start_time = time.time()

            try:
                model = models.Cellpose(model_type='cyto')
                print("  Using Cellpose class")
            except Exception as e1:
                print(f"  Cellpose class failed: {e1}")
                try:
                    model = models.CellposeModel(model_type='cyto')
                    print("  Using CellposeModel class")
                except Exception as e2:
                    print(f"  CellposeModel class failed: {e2}")
                    return False, None

            load_time = time.time() - start_time
            print(f"✅ Model loaded successfully in {load_time:.2f}s")
            return True, model
        except Exception as e:
            print(f"❌ Model loading failed: {e}")
            return False, None

    def test_inference(self, model: Any) -> bool:
        """测试推理功能
        
        Args:
            model: Cellpose模型对象
            
        Returns:
            bool: 测试是否成功
        """
        print("\n3. Testing inference...")
        try:
            # 创建测试图像
            test_img = np.random.randint(0, 255, (64, 64), dtype=np.uint8)
            print(f"  Test image size: {test_img.shape}")
            
            start_time = time.time()
            masks, flows, styles, diams = model.eval(
                test_img, 
                diameter=None, 
                channels=[0, 0]
            )
            inference_time = time.time() - start_time
            
            print(f"✅ Inference successful, time: {inference_time:.2f}s")
            print(f"  Detected {len(np.unique(masks))-1} objects")
            return True
        except Exception as e:
            print(f"❌ Inference failed: {e}")
            return False

    def test_command_line(self) -> bool:
        """测试命令行功能
        
        Returns:
            bool: 测试是否成功
        """
        print("\n4. Testing command line interface...")
        
        # 创建测试目录
        self.test_dir.mkdir(exist_ok=True)
        
        try:
            from PIL import Image
            
            # 创建测试图像
            test_img = np.random.randint(0, 255, (128, 128), dtype=np.uint8)
            test_file = self.test_dir / "test_image.tif"
            Image.fromarray(test_img).save(test_file)
            
            print(f"  Created test image: {test_file}")
            
            # 运行cellpose命令
            cmd = [
                sys.executable, "-m", "cellpose",
                "--image_path", str(test_file),
                "--savedir", str(self.test_dir),
                "--save_outlines",
                "--save_txt",
                "--verbose",
                "--chan", "0",
                "--chan2", "0"
            ]
            
            print(f"  Running command: {' '.join(cmd)}")
            
            start_time = time.time()
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=60
            )
            cmd_time = time.time() - start_time
            
            print(f"  Command execution time: {cmd_time:.2f}s")
            
            if result.stdout:
                print("  Standard output:")
                print("  " + "\n  ".join(result.stdout.split('\n')[:10]))
            
            if result.stderr:
                print("  Error output:")
                print("  " + "\n  ".join(result.stderr.split('\n')[:10]))
            
            # 检查输出文件
            outline_file = self.test_dir / "test_image_cp_outlines.txt"
            if outline_file.exists():
                print(f"✅ Generated outline file: {outline_file}")
                with open(outline_file) as f:
                    lines = f.readlines()
                    print(f"  Detected {len(lines)} cells")
                return True
            else:
                print("⚠️ No outline file generated")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Command execution timeout")
            return False
        except Exception as e:
            print(f"❌ Command execution failed: {e}")
            return False
        finally:
            # 清理测试文件
            self._cleanup()

    def _cleanup(self):
        """清理测试文件"""
        import shutil
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)

    def run_all_tests(self) -> bool:
        """运行所有测试
        
        Returns:
            bool: 所有测试是否通过
        """
        print("=" * 50)
        print("Cellpose Test Suite")
        print("=" * 50)
        
        # 导入测试
        import_ok, version = self.test_import()
        if not import_ok:
            return False
            
        # 检查版本兼容性
        if version and version.startswith("4."):
            print("\n⚠️ Warning: Detected Cellpose 4.x, which may have compatibility issues")
            
        # 模型加载测试
        load_ok, model = self.test_model_loading()
        if not load_ok:
            return False
            
        # 推理测试
        if not self.test_inference(model):
            return False
            
        # 命令行测试
        if not self.test_command_line():
            return False
            
        print("\n✅ All tests passed! Cellpose is working correctly")
        print("\nSuggestions:")
        print("1. If image processing hangs, the images might be too large")
        print("2. Consider testing with a smaller batch first")
        
        return True

def main():
    """主函数"""
    tester = CellposeTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
