#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for setup_and_run.py improvements
"""

import sys
import os
from pathlib import Path

# Add the current directory to the path so we can import setup_and_run
sys.path.insert(0, str(Path(__file__).parent))

from setup_and_run import EnvironmentManager

def test_pip_version_check():
    """Test pip version checking"""
    print("Testing pip version check...")
    manager = EnvironmentManager()
    
    # Test if virtual environment exists
    if not manager.venv_path.exists():
        print("Virtual environment not found, creating one...")
        if not manager.create_venv():
            print("Failed to create virtual environment")
            return False
    
    # Test pip version check
    pip_up_to_date = manager.check_pip_version()
    print(f"Pip up to date: {pip_up_to_date}")
    
    return True

def test_dependencies_check():
    """Test dependency checking"""
    print("\nTesting dependency check...")
    manager = EnvironmentManager()
    
    # Test if virtual environment exists
    if not manager.venv_path.exists():
        print("Virtual environment not found, skipping dependency check")
        return False
    
    # Test dependency check
    deps_installed = manager.check_dependencies_installed()
    print(f"Dependencies installed: {deps_installed}")
    
    return True

def main():
    """Run all tests"""
    print("=" * 50)
    print(" Testing Setup Script Improvements")
    print("=" * 50)
    
    try:
        # Test pip version check
        if not test_pip_version_check():
            print("Pip version check test failed")
            return 1
        
        # Test dependency check
        if not test_dependencies_check():
            print("Dependency check test failed")
            return 1
        
        print("\n✅ All tests passed!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
