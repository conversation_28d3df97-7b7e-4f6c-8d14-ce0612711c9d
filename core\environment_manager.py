#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境管理模块
负责Python虚拟环境的创建和管理
"""

import sys
import platform
import venv
from pathlib import Path
from typing import Tuple

class EnvironmentManager:
    """环境管理类"""
    def __init__(self, project_root: Path, state_manager):
        self.project_root = project_root
        self.state_manager = state_manager
        self.venv_path = project_root / ".venv"
        self.is_windows = platform.system().lower() == "windows"
        self.python_command = "python.exe" if self.is_windows else "python"

    def check_environment(self) -> bool:
        """检查环境是否已经配置好
        
        Returns:
            bool: 环境是否配置正确
        """
        # 检查Python版本
        version_ok, message = self.check_python_version()
        if not version_ok:
            print(message)
            if not input("Continue anyway? [y/N] ").lower().startswith('y'):
                return False

        # 检查虚拟环境
        if not self.state_manager.is_component_ready("virtual_env"):
            return False

        # 检查虚拟环境是否存在且可用
        if not self.venv_path.exists():
            return False

        return True

    def setup_environment(self) -> bool:
        """设置和配置环境
        
        Returns:
            bool: 设置是否成功
        """
        print("\n[1/1] Setting up virtual environment...")
        return self.create_venv()

    def get_python_version(self) -> str:
        """获取Python版本信息
        
        Returns:
            str: Python版本字符串
        """
        version = sys.version_info
        return f"{version.major}.{version.minor}.{version.micro}"

    def check_python_version(self) -> Tuple[bool, str]:
        """检查Python版本兼容性
        
        Returns:
            Tuple[bool, str]: (是否兼容, 提示信息)
        """
        version = sys.version_info
        if version.major != 3 or version.minor not in [9, 10, 11]:
            message = (
                f"⚠️ Warning: Current Python version ({self.get_python_version()}) "
                f"may not be compatible\nPython 3.10 is recommended"
            )
            return False, message
        return True, f"Python version {self.get_python_version()} is compatible"

    def get_venv_python(self) -> str:
        """获取虚拟环境中的Python解释器路径
        
        Returns:
            str: Python解释器的绝对路径
        """
        if self.is_windows:
            python_path = self.venv_path / "Scripts" / self.python_command
        else:
            python_path = self.venv_path / "bin" / self.python_command
        return str(python_path)

    def create_venv(self) -> bool:
        """创建虚拟环境
        
        如果虚拟环境已存在，则跳过创建
        
        Returns:
            bool: 创建是否成功
        """
        # 检查是否已创建
        if self.state_manager.is_component_ready("virtual_env"):
            print("Virtual environment already exists")
            return True

        print(f"\nCreating virtual environment: {self.venv_path}")
        
        if self.venv_path.exists():
            print("Existing virtual environment found, skipping creation")
            # 更新状态
            self.state_manager.update_component_state("virtual_env", {
                "created": True,
                "path": str(self.venv_path)
            })
            return True
            
        try:
            venv.create(self.venv_path, with_pip=True)
            # 更新状态
            self.state_manager.update_component_state("virtual_env", {
                "created": True,
                "path": str(self.venv_path)
            })
            return True
        except Exception as e:
            print(f"Failed to create virtual environment: {e}")
            return False

    def is_venv_active(self) -> bool:
        """检查是否在虚拟环境中运行
        
        Returns:
            bool: 是否在虚拟环境中
        """
        return (hasattr(sys, 'real_prefix') or 
                (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))

    def get_venv_info(self) -> dict:
        """获取虚拟环境信息
        
        Returns:
            dict: 虚拟环境信息字典
        """
        return {
            "path": str(self.venv_path),
            "python_path": self.get_venv_python(),
            "is_active": self.is_venv_active(),
            "python_version": self.get_python_version()
        }
