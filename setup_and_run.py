#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EasyReporter Environment Setup and Launch Script
Can be executed in any Python virtual environment
"""

import os
import sys
import subprocess
import platform
import site
import venv
from pathlib import Path
import shutil

class EnvironmentManager:
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.venv_path = self.project_root / ".venv"
        self.requirements_file = self.project_root / "requirements.txt"
        self.is_windows = platform.system().lower() == "windows"
        self.python_command = "python.exe" if self.is_windows else "python"
        self.pip_command = "pip.exe" if self.is_windows else "pip"

    def run_command(self, command, check_output=False, shell=False):
        """执行命令并显示输出"""
        try:
            if check_output:
                return subprocess.check_output(command, shell=shell, text=True)
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                shell=shell,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            for line in process.stdout:
                print(line.strip())
            
            process.wait()
            return process.returncode == 0
        except Exception as e:
            print(f"Command execution error: {e}")
            return False if not check_output else None

    def get_python_version(self):
        """获取Python版本信息"""
        version = sys.version_info
        return f"{version.major}.{version.minor}.{version.micro}"

    def check_python_version(self):
        """检查Python版本兼容性"""
        version = sys.version_info
        if version.major != 3 or version.minor not in [9, 10, 11]:
            print(f"⚠️ Warning: Current Python version ({self.get_python_version()}) may not be compatible")
            print("Python 3.10 is recommended")
            return False
        return True

    def create_venv(self):
        """Create virtual environment"""
        print(f"\nCreating virtual environment: {self.venv_path}")
        
        if self.venv_path.exists():
            print("Existing virtual environment found, skipping creation")
            return True
            
        try:
            venv.create(self.venv_path, with_pip=True)
            return True
        except Exception as e:
            print(f"Failed to create virtual environment: {e}")
            return False

    def get_venv_python(self):
        """获取虚拟环境中的Python路径"""
        if self.is_windows:
            python_path = self.venv_path / "Scripts" / self.python_command
        else:
            python_path = self.venv_path / "bin" / self.python_command
        return str(python_path)

    def get_venv_pip(self):
        """获取虚拟环境中的pip路径"""
        if self.is_windows:
            return str(self.venv_path / "Scripts" / self.pip_command)
        else:
            return str(self.venv_path / "bin" / self.pip_command)

    def check_pip_version(self):
        """检查pip版本是否需要更新"""
        try:
            pip_path = self.get_venv_pip()
            result = subprocess.run(
                [pip_path, "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode == 0:
                # 解析pip版本，格式通常为 "pip 25.2 from ..."
                version_line = result.stdout.strip()
                if "pip" in version_line:
                    version_part = version_line.split()[1]
                    current_version = tuple(map(int, version_part.split('.')))
                    target_version = (25, 2)  # 目标版本25.2
                    return current_version >= target_version
        except Exception:
            pass
        return False

    def check_dependencies_installed(self):
        """检查依赖是否已安装"""
        try:
            pip_path = self.get_venv_pip()

            # 读取requirements.txt文件
            if not self.requirements_file.exists():
                return False

            with open(self.requirements_file, 'r', encoding='utf-8') as f:
                requirements = f.readlines()

            # 解析依赖包名
            required_packages = []
            for line in requirements:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 提取包名（去除版本要求）
                    package_name = line.split('>=')[0].split('==')[0].split('<')[0].strip()
                    if package_name:
                        required_packages.append(package_name)

            if not required_packages:
                return False

            # 检查已安装的包
            result = subprocess.run(
                [pip_path, "list", "--format=freeze"],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                return False

            installed_packages = set()
            for line in result.stdout.splitlines():
                if '==' in line:
                    package_name = line.split('==')[0].strip().lower()
                    installed_packages.add(package_name)

            # 检查所有必需的包是否都已安装
            missing_packages = []
            for package in required_packages:
                # 处理包名的不同格式（如opencv-python）
                package_lower = package.lower().replace('_', '-')
                if package_lower not in installed_packages:
                    missing_packages.append(package)

            if missing_packages:
                print(f"Missing packages: {', '.join(missing_packages)}")
                return False

            return True

        except Exception as e:
            print(f"Error checking dependencies: {e}")
            return False

    def install_dependencies(self):
        """安装依赖"""
        # 首先检查依赖是否已安装
        if self.check_dependencies_installed():
            print("All dependencies are already installed, skipping installation")
            return True

        pip_path = self.get_venv_pip()

        def run_pip_install(args):
            # 设置环境变量启用进度条
            env = os.environ.copy()
            env["PIP_PROGRESS_BAR"] = "on"  # 启用进度条
            env["PYTHONUNBUFFERED"] = "1"   # 禁用输出缓冲
            env["FORCE_COLOR"] = "1"        # 强制彩色输出
            env["TERM"] = "xterm-256color"  # 设置终端类型支持颜色

            try:
                # 使用 subprocess.run 并直接输出到终端
                result = subprocess.run(
                    [pip_path] + args,
                    env=env,
                    text=True,
                    bufsize=0,  # 完全禁用缓冲
                    stdout=None,  # 直接输出到终端
                    stderr=None   # 直接输出到终端
                )

                return result.returncode == 0
            except Exception as e:
                print(f"安装出错: {e}")
                return False

        # 检查并更新pip（仅在需要时）
        if not self.check_pip_version():
            print("Updating pip...")
            run_pip_install(["install", "--upgrade", "pip", "--quiet"])
        else:
            print("Pip is already up to date")

        # Install base dependencies
        print("\nInstalling project dependencies...")
        success = run_pip_install([
            "install", "-r", str(self.requirements_file),
            "--progress-bar=on",             # 启用进度条（使用=号格式）
            "--disable-pip-version-check",   # 禁用版本检查
            "--no-cache-dir"                 # 禁用缓存以确保实时下载
        ])

        if not success:
            print("\nTrying installation with mirror source...")
            success = run_pip_install([
                "install", "-r", str(self.requirements_file),
                "--progress-bar=on",
                "--disable-pip-version-check",
                "--no-cache-dir",
                "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"
            ])

        return success

    def setup_gpu_support(self):
        """Configure GPU support"""
        print("\nConfiguring GPU support...")
        try:
            # Set environment variable to disable Python output buffering
            my_env = os.environ.copy()
            my_env["PYTHONUNBUFFERED"] = "1"
            
            process = subprocess.Popen(
                [self.get_venv_python(), "-u", "install_gpu_support.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                env=my_env,
                bufsize=0,  # 完全禁用缓冲
                text=True,
                universal_newlines=True
            )
            
            import sys
            # 实时输出日志
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
                    sys.stdout.flush()  # 强制刷新输出
            
            process.wait()
            return process.returncode == 0
        except Exception as e:
            print(f"GPU configuration error: {e}")
            return False

    def start_app(self):
        """Start the application"""
        print("\nLaunching EasyReporter...")
        return self.run_command([
            self.get_venv_python(),
            "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501"
        ])

    def run(self):
        """运行完整的启动流程"""
        print("=" * 50)
        print(" EasyReporter Environment Setup and Launch")
        print("=" * 50)
        print(f"Python version: {self.get_python_version()}")
        
        if not self.check_python_version():
            if not input("Continue anyway? [y/N] ").lower().startswith('y'):
                return False
        
        if not self.create_venv():
            return False
            
        if not self.install_dependencies():
            return False
            
        if not self.setup_gpu_support():
            print("警告: GPU支持配置失败，将使用CPU模式")
            
        return self.start_app()

def main():
    manager = EnvironmentManager()
    try:
        success = manager.run()
        if not success:
            print("\nLaunch failed, please check the error messages above")
            input("Press Enter to exit...")
            return 1
        return 0
    except KeyboardInterrupt:
        print("\nOperation interrupted by user")
        return 1
    except Exception as e:
        print(f"\nError occurred: {e}")
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
