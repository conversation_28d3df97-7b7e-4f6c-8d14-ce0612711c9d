#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cellpose管理模块
负责Cellpose的安装、测试和版本管理
"""

import subprocess
from pathlib import Path
from typing import Optional, Tuple

class CellposeManager:
    """Cellpose管理类"""
    def __init__(self, package_manager, state_manager, env_manager):
        self.package_manager = package_manager
        self.state_manager = state_manager
        self.env_manager = env_manager
        self.default_version = "3.0.8"

    def check_cellpose_import(self) -> Tuple[bool, Optional[str]]:
        """检查Cellpose是否可以导入
        
        Returns:
            Tuple[bool, Optional[str]]: (是否可导入, 版本号)
        """
        try:
            import cellpose
            try:
                version = cellpose.__version__
            except AttributeError:
                try:
                    version = cellpose.version
                except AttributeError:
                    try:
                        import pkg_resources
                        version = pkg_resources.get_distribution('cellpose').version
                    except:
                        version = self.default_version
            return True, version
        except Exception as e:
            print(f"Cellpose import failed: {e}")
            return False, None

    def install_cellpose(self) -> bool:
        """安装指定版本的Cellpose
        
        Returns:
            bool: 安装是否成功
        """
        print(f"Installing Cellpose {self.default_version}...")
        return self.package_manager.run_pip_install(f"cellpose=={self.default_version}")

    def run_cellpose_test(self) -> bool:
        """运行Cellpose测试
        
        Returns:
            bool: 测试是否通过
        """
        print("\nRunning Cellpose tests...")
        try:
            result = subprocess.run(
                [self.env_manager.get_venv_python(), "quick_cellpose_test.py"],
                capture_output=True,
                text=True
            )
            
            # 输出测试结果
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(result.stderr)
                
            return result.returncode == 0
        except Exception as e:
            print(f"Test execution failed: {e}")
            return False

    def check_and_setup(self) -> bool:
        """检查并配置Cellpose环境
        
        Returns:
            bool: 配置是否成功
        """
        # 检查是否已经测试通过
        if self.state_manager.is_component_ready("cellpose"):
            print("Cellpose is already configured and tested.")
            return True

        # 检查导入
        import_ok, version = self.check_cellpose_import()
        if not import_ok:
            print("Cellpose not found, attempting installation...")
            if not self.install_cellpose():
                return False
            import_ok, version = self.check_cellpose_import()
            if not import_ok:
                return False

        # 检查版本兼容性
        if version and version.startswith("4."):
            print(f"⚠️ Detected Cellpose {version}, which may have compatibility issues")
            print("Downgrading to version 3.0.8...")
            if not self.install_cellpose():
                return False
            import_ok, version = self.check_cellpose_import()
            if not import_ok:
                return False

        # 运行测试
        if not self.run_cellpose_test():
            return False

        # 更新状态
        self.state_manager.update_component_state("cellpose", {
            "tested": True,
            "version": version or self.default_version
        })
        
        return True
