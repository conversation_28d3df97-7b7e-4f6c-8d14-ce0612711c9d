#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的状态管理模块
管理所有组件的状态信息
"""

import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, Union

class StateManager:
    """统一的状态管理类"""
    def __init__(self, project_root: Path):
        self.state_file = project_root / ".easyreporter_state.json"
        self.default_state = {
            "installation": {
                "completed": False,
                "timestamp": None
            },
            "gpu": {
                "status": None,  # success, failed, or None
                "timestamp": None,
                "version": None
            },
            "cellpose": {
                "tested": False,
                "version": None,
                "timestamp": None
            },
            "virtual_env": {
                "created": False,
                "path": None,
                "timestamp": None
            }
        }
        self._load_state()

    def _load_state(self) -> None:
        """加载状态文件"""
        try:
            if self.state_file.exists():
                with open(self.state_file) as f:
                    self.state = json.load(f)
            else:
                self.state = self.default_state
        except Exception:
            self.state = self.default_state

    def _save_state(self) -> None:
        """保存状态到文件"""
        with open(self.state_file, 'w') as f:
            json.dump(self.state, f, indent=2)

    def get_state(self, *keys: str) -> Any:
        """获取状态值
        
        Args:
            *keys: 状态键路径，例如 "gpu", "status"
            
        Returns:
            状态值或None（如果路径不存在）
        """
        current = self.state
        for key in keys:
            if key in current:
                current = current[key]
            else:
                return None
        return current

    def update_state(self, updates: Dict[str, Any]) -> None:
        """更新状态
        
        Args:
            updates: 要更新的状态字典，支持嵌套更新
        """
        def update_dict(target: Dict, source: Dict) -> None:
            for key, value in source.items():
                if isinstance(value, dict) and key in target:
                    update_dict(target[key], value)
                else:
                    target[key] = value
                    # 如果更新的是状态值，自动更新时间戳
                    if isinstance(target, dict) and "timestamp" in target:
                        target["timestamp"] = time.time()
        
        update_dict(self.state, updates)
        self._save_state()

    def get_component_state(self, component: str) -> Dict:
        """获取组件的完整状态
        
        Args:
            component: 组件名称，例如 "gpu", "cellpose"
            
        Returns:
            组件的状态字典
        """
        return self.state.get(component, {})

    def update_component_state(self, component: str, state: Dict[str, Any]) -> None:
        """更新组件状态
        
        Args:
            component: 组件名称
            state: 新的状态字典
        """
        self.update_state({component: state})

    def is_component_ready(self, component: str) -> bool:
        """检查组件是否已经准备就绪
        
        Args:
            component: 组件名称
            
        Returns:
            bool: 组件是否就绪
        """
        state = self.get_component_state(component)
        if component == "installation":
            return state.get("completed", False)
        elif component == "gpu":
            return state.get("status") == "success"
        elif component == "cellpose":
            return state.get("tested", False)
        elif component == "virtual_env":
            return state.get("created", False)
        return False

    def reset_component(self, component: str) -> None:
        """重置组件状态到默认值
        
        Args:
            component: 组件名称
        """
        if component in self.default_state:
            self.update_component_state(component, self.default_state[component].copy())

    def reset_all(self) -> None:
        """重置所有状态到默认值"""
        self.state = self.default_state.copy()
        self._save_state()
